
/* scrape */
import { join } from "path";

import { dirMustExist, launchBrowser, sleep, pad<PERSON>ero } from "spider-core";
import {
    __dirname,
    executablePath,
    headless,
    singpassId,
    password,
    facilityId,
    court,
    QrWaitTime,
    date,
    preferredTimes,
    refreshStartHour,
    refreshStartMinute,
    refreshStartSecond,
    refreshIntervalSeconds,
    refreshMaxCount,
} from "../config.js";

const appDataDir = join(__dirname, "./app-data");
dirMustExist(appDataDir);
const userDataDir = join(appDataDir, "singpass");
dirMustExist(userDataDir);

const proxiesFile = join(__dirname, "./proxies.txt");

const waitForRefreshStartTime = () => {
    return new Promise((resolve) => {
        if (typeof refreshStartHour !== "number" || typeof refreshStartMinute !== "number") {
            console.warn(
                "\nERROR => REFRESH_START_HOUR and REFRESH_START_MINUTE are not numbers. Please edit them in the .env file\n"
            );
            return resolve(false);
        }

        if (isNaN(refreshStartHour) || isNaN(refreshStartMinute)) {
            console.error(
                "REFRESH_START_HOUR and REFRESH_START_MINUTE are not numbers. Please edit them in the .env file"
            );
            process.exit(0);
        }

        const refStartSec =
            typeof refreshStartSecond !== "number" || isNaN(refreshStartSecond) ? 0 : refreshStartSecond;

        const dt = new Date();
        const hrs = dt.getHours();
        const mins = dt.getMinutes();
        const sec = dt.getSeconds();

        if (
            hrs === refreshStartHour &&
            ((mins === refreshStartMinute && sec >= refStartSec) || mins > refreshStartMinute)
        )
            return resolve(true);

        let hrsDiff = refreshStartHour - hrs;
        if (hrsDiff < 0) hrsDiff += 24;

        return sleep(hrsDiff > 1 ? (hrsDiff - 1) * 60 * 60 * 1000 : 250)
            .then(waitForRefreshStartTime)
            .then(resolve);
    });
};

const scrape = async (createNewPage) => {
    const [page] = await createNewPage();
    if (!page) return console.error("Page was not opened");

    const waitForLoader = () => page.waitForSelector(".points", { visible: true }).catch(() => { });

    // const encFacilityId = encodeURIComponent(facilityId);
    // const encDate = encodeURIComponent(date);
    // const url = `https://www.onepa.gov.sg/facilities/availability?facilityId=${encFacilityId}&date=${encDate}`;
    const url = `https://m.safra.sg/home`;
    const whCourt = court + 1;
    const QrTime = QrWaitTime;

    try {
        console.log(`Opening ${url} ...`);
        await page.goto(url, { waitUntil: "domcontentloaded", timeout: 5 * 60 * 1000 });
    } catch (err) {
        return console.error("Unable to open url", err);
    }

    // const LoggedIn = ".section-sidebar";
    const LoggedIn = 'input[name="username"]';

    // await page.waitForSelector(".section-sidebar", { visible: true }).catch(() => { });

    // .section-sidebar
    const logInn = async () => {
        try {
            // type into an element like a human user (with delay)
            console.log("Typing In Username...");
            await page.waitForSelector('input[name="username"]');
            await page.type('input[name="username"]', singpassId, { delay: 200 });
        } catch (err) {
            console.error("Unable to type in Username", err);
        }

        try {
            // type into an element like a human user (with delay)
            console.log("Typing In Password...");
            await page.type('input[name="password"]', password, { delay: 200 });
            console.log("Please solve the captcha to continue");
        } catch (err) {
            console.error("Unable to type in Password", err);
        }


        try {
            await page.waitForSelector(".section-sidebar", { timeout: 10 * 60 * 1000 });
            console.log("Login Success");
        } catch (e) {
            console.log(e);
        }
    };


    // Enhanced function to loop through elements and select based on innerText
    const selectElementByInnerText = async (selector, targetText, options = {}) => {
        const {
            method = "equals", // "equals", "includes", "startsWith", "endsWith", "regex"
            caseSensitive = false,
            returnFirst = true, // Return first match or all matches
            clickElement = true, // Whether to click the element or just return it
            scrollToElement = false // Whether to scroll to element before clicking
        } = options;

        try {
            console.log(`Searching for element with text: "${targetText}" using method: ${method}`);
            
            // Wait for elements to be present
            await page.waitForSelector(selector, { timeout: 10000 });
            
            const result = await page.evaluate((sel, text, opts) => {
                const elements = Array.from(document.querySelectorAll(sel));
                const matches = [];

                for (let i = 0; i < elements.length; i++) {
                    const element = elements[i];
                    let elementText = element.innerText.trim();
                    let searchText = text;

                    // Handle case sensitivity
                    if (!opts.caseSensitive) {
                        elementText = elementText.toLowerCase();
                        searchText = searchText.toLowerCase();
                    }

                    let isMatch = false;

                    // Check match based on method
                    switch (opts.method) {
                        case "equals":
                            isMatch = elementText === searchText;
                            break;
                        case "includes":
                            isMatch = elementText.includes(searchText);
                            break;
                        case "startsWith":
                            isMatch = elementText.startsWith(searchText);
                            break;
                        case "endsWith":
                            isMatch = elementText.endsWith(searchText);
                            break;
                        case "regex":
                            const regex = new RegExp(searchText, opts.caseSensitive ? 'g' : 'gi');
                            isMatch = regex.test(elementText);
                            break;
                        default:
                            isMatch = elementText === searchText;
                    }

                    if (isMatch) {
                        // Add a unique data attribute to identify this element
                        element.setAttribute('data-select-target', `match-${i}`);

                        matches.push({
                            index: i,
                            text: element.innerText.trim(),
                            dataAttribute: `match-${i}`
                        });

                        // If we only want the first match, return it
                        if (opts.returnFirst) {
                            return { success: true, matches: [matches[0]] };
                        }
                    }
                }

                return { success: matches.length > 0, matches: matches };
            }, selector, targetText, { method, caseSensitive, returnFirst, clickElement, scrollToElement });

            // Handle clicking and scrolling outside of page.evaluate for better control
            if (result.success && clickElement) {
                const firstMatch = result.matches[0];
                const elementSelector = `[data-select-target="${firstMatch.dataAttribute}"]`;

                if (scrollToElement) {
                    // Scroll to element first
                    await page.evaluate((sel) => {
                        const element = document.querySelector(sel);
                        if (element) {
                            element.scrollIntoView({
                                behavior: 'smooth',
                                block: 'center',
                                inline: 'nearest'
                            });
                        }
                    }, elementSelector);

                    // Wait for smooth scrolling to complete
                    await page.waitForTimeout(1000);
                }

                // Click the element
                await page.click(elementSelector);
                console.log(`Clicked element with text: "${firstMatch.text}"`);

                // Clean up the data attribute
                await page.evaluate((sel) => {
                    const element = document.querySelector(sel);
                    if (element) {
                        element.removeAttribute('data-select-target');
                    }
                }, elementSelector);

                return result.matches;
            }
            
            if (result.success) {
                console.log(`Found ${result.matches.length} matching element(s)`);
                if (result.clicked) {
                    console.log(`Clicked element with text: "${result.matches[0].text}"`);
                }
                return result.matches;
            } else {
                console.log(`No elements found with text: "${targetText}"`);
                return [];
            }
            
        } catch (error) {
            console.error(`Error selecting element by innerText: ${error.message}`);
            return [];
        }
    };

    // Legacy function for backward compatibility
    // var clickElementByInnerText = (page, { selector, texts, method = "equals", noOfClicks = 0 }) => {
    //     const validMethods = ["includes", "startsWith", "endsWith", "regex", "equals"];
    //     if (!validMethods.includes(method))
    //         return Promise.reject("Invalid method was sent for clickElementByInnerText. " + validMethods.join(", "));

    //     return page.$$eval(
    //         selector,
    //         (elms, texts, method, noOfClicks) => {
    //             let localNoOfClicks = 1;
    //             const regexMap = texts.map((t) => new RegExp(t, "i"));
    //             for (const elm of elms) {
    //                 const elmTxt = elm.innerText.trim();
    //                 if (method === "regex") {
    //                     for (const reg of regexMap)
    //                         if (reg.test(elmTxt)) {
    //                             elm.click();
    //                             localNoOfClicks++;
    //                         }
    //                 } else if (method === "equals") {
    //                     for (const text of texts)
    //                         if (elmTxt === text) {
    //                             elm.click();
    //                             localNoOfClicks++;
    //                         }
    //                 } else {
    //                     for (const text of texts)
    //                         if (elmTxt[method](text)) {
    //                             elm.click();
    //                             localNoOfClicks++;
    //                         }
    //                 }

    //                 if (noOfClicks > 0 && noOfClicks < localNoOfClicks) break;
    //             }
    //         },
    //         texts,
    //         method,
    //         noOfClicks
    //     );
    // };

    page.waitForSelector(LoggedIn)
        .then(logInn)
        .catch(() => { });


    try {
        // wait for an element and click it when it appears
        console.log("Clicking Facility Type");
        await page.waitForSelector('[value="SYCC - Games Room"]', { timeout: 2 * 60 * 1000 }).then((x) => x.click());
    } catch (err) {
        console.error("Unable to click Facility Type", err);
    }

    try {
        // click an element by innerText for Facility Type
        console.log("Clicking on specified Facility Type");
        await selectElementByInnerText(
            ".theme--values--2_qrExHl li",
            "TP - Badminton",
            {
                method: "equals",
                clickElement: true,
                scrollToElement: true,
                caseSensitive: false
            }
        );
    } catch (err) {
        console.error("Unable to click the specified Facility Type", err);
    }

     try {
        console.log("Clicking Facility");
        await page.waitForSelector('[value="All"]', { timeout: 2 * 60 * 1000 }).then((x) => x.click());
    } catch (err) {
        console.error("Unable to click Facility", err);
    }

      try {
        // click an element by innerText for Facility
        console.log("Clicking on specified Facility");
        await selectElementByInnerText(
            ".theme--values--2_qrExHl li",
            "TP - Badminton Court 1",
            {
                method: "equals",
                clickElement: true,
                scrollToElement: true,
                caseSensitive: false
            }
        );
    } catch (err) {
        console.error("Unable to click the specified Facility", err);
    }


    console.log(`\n\nWaiting till ${padZero(refreshStartHour)}:${padZero(refreshStartMinute)}`);
    await waitForRefreshStartTime();


  try {
        // click an element by innerText for Facility
        console.log("Clicking on specified Facility");
        await selectElementByInnerText(
            ".theme--primary--29RI99wV",
            "Search Availability",
            {
                method: "equals",
                clickElement: true,
                scrollToElement: true,
                caseSensitive: false
            }
        );
    } catch (err) {
        console.error("Unable to click the specified Facility", err);
    }



    let indices = [];
    const refreshPage = async () => {
        console.log("\nReloading ...");
        await page.reload({ waitUntil: "domcontentloaded", timeout: 60 * 1000 }).catch(() => { });
        console.log('I got here');
    };



    let refreshAndSelectSlotCount = 0;
    const refreshAndSelectSlot = async () => {
        await waitForLoader();

        try {
            console.log("Selecting check box");
            await page.waitForSelector(".booking-avail-table__table-slots", { timeout: 10 * 60 * 1000 });

            indices = await page.evaluate((timesArr) => {
                const domTimes = document.querySelectorAll(
                    ".booking-avail-table__table-slots-col--side-mark .booking-avail-table__table-default"
                );

                // const indices = [];
                // domTimes.forEach((domTime, index) => {
                //     const time = domTime.innerText.toUpperCase().replace(/\s+/g, "");
                //     if (timesArr.length === 0) indices.push({ index });
                //     else if (timesArr.includes(time)) indices.push({ index });
                // });
                const preferredIndices = [];
                const randomIndices = [];
                domTimes.forEach((domTime, index) => {
                    const time = domTime.innerText.toUpperCase().replace(/\s+/g, "");
                    if (timesArr.includes(time)) preferredIndices.push({ time, index });
                    randomIndices.push({ time, index });
                });
                const indices = [...preferredIndices, ...randomIndices];

                const _1mphElements = document.querySelectorAll(
                    `.booking-avail-table__table-slots-col:nth-of-type(2) .booking-avail-table__table-timeslot`
                );
                _1mphElements.forEach((_1mphElement, _1mphIndex) => {
                    const index = indices.findIndex((i) => i.index === _1mphIndex);
                    const hasIndex = index >= 0;
                    if (!hasIndex) return;
                    indices[index].valid1mph = !!_1mphElement.querySelector(
                        ".booking-avail-table__legend-icon--non-peak"
                    );
                });

                const _2mphElements = document.querySelectorAll(
                    ".booking-avail-table__table-slots-col:nth-of-type(3) .booking-avail-table__table-timeslot"
                );
                _2mphElements.forEach((_2mphElement, _2mphIndex) => {
                    const index = indices.findIndex((i) => i.index === _2mphIndex);
                    const hasIndex = index >= 0;
                    if (!hasIndex) return;
                    indices[index].valid2mph = !!_2mphElement.querySelector(
                        ".booking-avail-table__legend-icon--non-peak"
                    );
                });

                const _3mphElements = document.querySelectorAll(
                    ".booking-avail-table__table-slots-col:nth-of-type(4) .booking-avail-table__table-timeslot"
                );
                _3mphElements.forEach((_3mphElement, _3mphIndex) => {
                    const index = indices.findIndex((i) => i.index === _3mphIndex);
                    const hasIndex = index >= 0;
                    if (!hasIndex) return;
                    indices[index].valid3mph = !!_3mphElement.querySelector(
                        ".booking-avail-table__legend-icon--non-peak"
                    );
                });

                const _4mphElements = document.querySelectorAll(
                    ".booking-avail-table__table-slots-col:nth-of-type(5) .booking-avail-table__table-timeslot"
                );
                _4mphElements.forEach((_4mphElement, _4mphIndex) => {
                    const index = indices.findIndex((i) => i.index === _4mphIndex);
                    const hasIndex = index >= 0;
                    if (!hasIndex) return;
                    indices[index].valid4mph = !!_4mphElement.querySelector(
                        ".booking-avail-table__legend-icon--non-peak"
                    );
                });

                return indices.filter((i) => i.valid1mph === true || i.valid2mph === true || i.valid3mph === true || i.valid4mph === true);
            }, preferredTimes);
        } catch (err) {
            console.error("Unable to get valid slots", err);
            return false;
        }

        refreshAndSelectSlotCount++;

        if (indices.length < 1) {
            console.log("No available slots");
            if (refreshMaxCount > 0 && refreshAndSelectSlotCount > refreshMaxCount) return false;

            if (refreshIntervalSeconds > 0)
                return page
                    .waitForTimeout(refreshIntervalSeconds * 1000)
                    .then(refreshPage)
                    .then(refreshAndSelectSlot);

            return refreshPage().then(refreshAndSelectSlot);
        }

        return true;
    };

    const isSlotSelected = await refreshAndSelectSlot();
    if (!isSlotSelected) return;

    let clickCount = 0;

    for (const index of indices) {
        // const click2mph = () =>
        //     page
        //         .$eval(
        //             `.booking-avail-table__table-slots-col:nth-of-type(3) .booking-avail-table__table-timeslot:nth-of-type(${nthIndex}) button`,
        //             (x) => x.click()
        //         )
        //         .then(() => clickCount++)
        //         .catch(() => {});

        const nthIndex = index.index + 1;
        const slot = async () => {
            console.log(`clicking ${court}mph on ${index.time} ...`);
            await page.$eval(
                `.booking-avail-table__table-slots-col:nth-of-type(${whCourt}) .booking-avail-table__table-timeslot:nth-of-type(${nthIndex}) button`,
                (x) => x.click()
            );
            clickCount++;
        };

        // if (index.valid1mph || index.valid2mph || index.valid3mph)
        try {
            if (index.valid1mph && court === 1) await slot();
            else if (index.valid2mph && court === 2) await slot();
            else if (index.valid3mph && court === 3) await slot();
            else if (index.valid4mph && court === 4) await slot();

        } catch (err) {
           
        }


        if (clickCount > 1) break;
    }

    try {
        console.log("Clicking booking for myself");
        const elementHandle = ".st-form-group__input .st-radio__circle";
        await page.waitForSelector(elementHandle).then(() => page.$eval(elementHandle, (x) => x.click()));
    } catch (e) {
        console.error("Unable to click booking for myself", e);
    }

    try {
        console.log("Clicking book now");
        await page
            .waitForSelector('button[data-testid="proceedBtn"]')
            .then((x) => Promise.all([page.waitForNavigation().catch(() => { }), x.click()]));
    } catch (e) {
        console.error("Unable to click book now", e);
    }

    await waitForLoader();

    try {
        const errorText = await page
            .waitForSelector(".error-popup", { timeout: 2500 })
            .then((x) => x.evaluate((x) => x.innerText));
        console.error("ERROR WHILE MAKING RESERVATION:", errorText);
    } catch (err) { }
};

const scrapeAmazonProduct = async () => {
    if (!singpassId) return console.error("SINGPASS_ID is required. Please set it in the .env file");
    if (!password) return console.error("PASSWORD is required. Please set it in the .env file");
    if (!facilityId) return console.error("FACILITY_ID is required. Please set it in the .env file");
    if (!date) return console.error("DATE is required. Please set it in the .env file");

    if (!/\s*\d{1,2}\s*\/\s*\d{1,2}\s*\/\s*\d{4}\s*/.test(date))
        return console.error("DATE is invalid. Please edit it in the .env file");

    const invalidPrefTimesIndex =
        preferredTimes.findIndex((x) => !/^\s*\d{1,2}\s*:\s*\d{1,3}\s*(a|p)m\s*$/i.test(x)) + 1;
    if (invalidPrefTimesIndex > 0)
        console.warn(
            `\nERROR => The time at position ${invalidPrefTimesIndex} in PREFERRED_TIMES is invalid. Please edit it in the .env file\n`
        );

    const [createNewPage] = await launchBrowser({ executablePath, headless, proxiesFile, userDataDir });

    await scrape(createNewPage);

    console.log();
    // await exitBrowser();
};

export default scrapeAmazonProduct;
