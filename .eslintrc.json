{"env": {"browser": true, "node": true, "es6": true, "es2017": true, "amd": true, "shared-node-browser": true}, "parserOptions": {"allowImportExportEverywhere": true, "ecmaVersion": "latest", "ecmaFeatures": {"jsx": true, "experimentalObjectRestSpread": true}, "sourceType": "module"}, "extends": ["eslint:recommended", "plugin:import/errors", "plugin:import/warnings"], "rules": {"import/named": "error", "import/namespace": ["error", {"allowComputed": false}], "import/extensions": ["error", "ignorePackages", {"js": "never", "jsx": "never", "ts": "never", "tsx": "never"}], "for-direction": "error", "getter-return": "error", "no-async-promise-executor": "off", "no-await-in-loop": "off", "no-compare-neg-zero": "error", "no-cond-assign": "warn", "no-constant-condition": "warn", "no-control-regex": "warn", "no-debugger": "warn", "no-dupe-args": "error", "no-dupe-else-if": "error", "no-dupe-keys": "error", "no-duplicate-case": "error", "no-empty": ["error", {"allowEmptyCatch": true}], "no-empty-character-class": "error", "no-ex-assign": "error", "no-extra-boolean-cast": ["error", {"enforceForLogicalOperands": true}], "no-extra-parens": "off", "no-extra-semi": "error", "no-func-assign": "error", "no-import-assign": "error", "no-inner-declarations": "error", "no-invalid-regexp": "error", "no-irregular-whitespace": "warn", "no-loss-of-precision": "warn", "no-misleading-character-class": "warn", "no-obj-calls": "error", "no-promise-executor-return": "off", "no-prototype-builtins": "error", "no-regex-spaces": "error", "no-setter-return": "error", "no-sparse-arrays": "off", "no-template-curly-in-string": "error", "no-unexpected-multiline": "error", "no-unreachable": "error", "no-unreachable-loop": "error", "no-unsafe-finally": "error", "no-unsafe-negation": "error", "no-unsafe-optional-chaining": ["error", {"disallowArithmeticOperators": true}], "no-useless-backreference": "error", "require-atomic-updates": "off", "use-isnan": "error", "valid-typeof": ["error", {"requireStringLiterals": true}], "no-delete-var": "warn", "no-label-var": "error", "no-shadow-restricted-names": "error", "no-undef": "error", "no-undef-init": "error", "no-undefined": "off", "no-unused-vars": ["error", {"args": "after-used", "caughtErrors": "none", "ignoreRestSiblings": true}], "array-callback-return": "error", "block-scoped-var": "error", "complexity": ["error", 500], "curly": ["warn", "multi", "consistent"], "default-case": "error", "default-case-last": "error", "default-param-last": "error", "dot-location": ["error", "property"], "dot-notation": ["error", {"allowKeywords": true}], "eqeqeq": ["error", "always"], "no-caller": "error", "no-else-return": ["error", {"allowElseIf": false}], "no-empty-function": ["error", {"allow": ["arrowFunctions"]}], "no-empty-pattern": "warn", "no-eq-null": "error", "no-eval": "warn", "no-extra-bind": "error", "no-extra-label": "error", "no-floating-decimal": "off", "no-global-assign": "warn", "no-implicit-coercion": "off", "no-implicit-globals": ["error", {"lexicalBindings": false}], "no-implied-eval": "warn", "no-invalid-this": "error", "no-iterator": "error", "no-labels": "off", "no-lone-blocks": "warn", "no-magic-numbers": "off", "no-multi-spaces": "error", "no-multi-str": "off", "no-new": "error", "no-new-func": "error", "no-new-wrappers": "error", "no-nonoctal-decimal-escape": "error", "no-octal": "warn", "no-param-reassign": "off", "no-proto": "error", "no-redeclare": "warn", "no-return-assign": "off", "no-return-await": "off", "no-self-assign": "error", "no-self-compare": "error", "no-sequences": "error", "no-unmodified-loop-condition": "warn", "no-unused-expressions": ["error", {"allowShortCircuit": true, "allowTernary": true}], "no-unused-labels": "error", "no-useless-call": "error", "no-useless-catch": "error", "no-useless-escape": "error", "no-useless-return": "error", "no-void": "error", "no-with": "error", "prefer-regex-literals": "error", "require-await": "warn", "wrap-iife": ["error", "inside"], "yoda": "off", "array-bracket-newline": ["warn", "consistent"], "array-bracket-spacing": ["error", "never"], "array-element-newline": ["error", "consistent"], "block-spacing": ["error", "always"], "brace-style": "error", "camelcase": "off", "comma-spacing": ["error", {"before": false, "after": true}], "comma-style": ["error", "last"], "computed-property-spacing": ["error", "never"], "eol-last": ["error", "always"], "func-call-spacing": ["error", "never"], "func-name-matching": ["error", "always"], "function-call-argument-newline": ["error", "consistent"], "id-length": ["off"], "implicit-arrow-linebreak": "off", "indent": "off", "line-comment-position": "off", "lines-around-comment": ["off"], "max-depth": ["error", 25], "max-lines": ["error", {"max": 5000, "skipBlankLines": true, "skipComments": true}], "max-nested-callbacks": ["error", 15], "max-params": ["off", 15], "max-statements-per-line": ["error", {"max": 1}], "multiline-comment-style": ["off"], "multiline-ternary": "off", "newline-per-chained-call": ["error", {"ignoreChainWithDepth": 5}], "no-array-constructor": "error", "no-bitwise": "error", "no-inline-comments": "off", "no-lonely-if": "error", "no-mixed-spaces-and-tabs": "error", "no-multi-assign": "error", "no-multiple-empty-lines": ["error", {"max": 5}], "no-nested-ternary": "error", "no-new-object": "error", "no-plusplus": "off", "no-trailing-spaces": ["error", {"skipBlankLines": false, "ignoreComments": false}], "no-unneeded-ternary": "warn", "no-whitespace-before-property": "error", "nonblock-statement-body-position": ["error", "any"], "object-curly-newline": ["error", {"multiline": true, "consistent": true}], "object-curly-spacing": ["error", "always"], "operator-assignment": ["warn", "always"], "operator-linebreak": "off", "padded-blocks": ["error", "never"], "prefer-exponentiation-operator": "error", "prefer-object-spread": "error", "quote-props": ["off"], "quotes": ["error", "double", {"avoidEscape": true, "allowTemplateLiterals": true}], "semi": ["error", "always"], "semi-style": ["error", "last"], "semi-spacing": "error", "space-before-blocks": "error", "space-before-function-paren": "off", "space-in-parens": ["error", "never"], "space-infix-ops": ["error", {"int32Hint": false}], "space-unary-ops": ["error", {"words": true, "nonwords": false}], "spaced-comment": ["off"], "switch-colon-spacing": "error", "template-tag-spacing": ["error", "always"], "arrow-body-style": "off", "arrow-parens": ["warn", "always"], "arrow-spacing": "error", "no-confusing-arrow": "off", "no-const-assign": "error", "no-duplicate-imports": ["error", {"includeExports": true}], "no-useless-computed-key": "error", "no-useless-rename": "error", "object-shorthand": ["warn", "always", {"avoidQuotes": true, "avoidExplicitReturnArrows": true}], "prefer-arrow-callback": "warn", "prefer-const": ["warn", {"destructuring": "all"}], "prefer-destructuring": ["warn", {"array": false, "object": true}, {"enforceForRenamedProperties": false}], "prefer-rest-params": "warn", "prefer-spread": "warn", "prefer-template": "off", "rest-spread-spacing": ["error", "never"], "template-curly-spacing": "error"}}