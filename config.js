import "dotenv/config";

export const crawlerName = process.env.CRAWLER_NAME;
if (crawlerName) console.log(`---------------------${crawlerName}---------------------`);


// import { dirname } from "path";
// import { fileURLToPath } from "url";

// import.meta.url
const __dirname = process.cwd(); // dirname(fileURLToPath(import.meta.url));

const executablePath = process.env.EXECUTABLE_PATH;
const headless = !(process.env.CHROME_NON_HEADLESS * 1);

const singpassId = process.env.SINGPASS_ID;
const password = process.env.PASSWORD;

const facilityId = process.env.FACILITY_ID || "ChangiSimeiCC_BADMINTONCOURTS";
const date = process.env.DATE;
const preferredTimes = (process.env.PREFERRED_TIMES || "7:30pm,8:30pm")
    .split(/\s*,\s*/)
    .map((t) => t.toUpperCase().replace(/\s+/g, ""))
    .filter((x) => !!x);

const refreshStartHour = process.env.REFRESH_START_HOUR * 1;
const refreshStartMinute = process.env.REFRESH_START_MINUTE * 1;
const refreshStartSecond = process.env.REFRESH_START_SECOND * 1;
const refreshIntervalSeconds = process.env.REFRESH_INTERVAL_SECONDS * 1;
const refreshMaxCount = (process.env.REFRESH_MAX_COUNT || 50) * 1;
const court = process.env.COURT * 1;
const QrWaitTime = process.env.QR_WAIT_TIME * 1;


export {
    __dirname,
    executablePath,
    headless,
    singpassId,
    password,
    facilityId,
    court,
    QrWaitTime,
    date,
    preferredTimes,
    refreshStartHour,
    refreshStartMinute,
    refreshStartSecond,
    refreshIntervalSeconds,
    refreshMaxCount,
};
