{"name": "onepa-crawler", "type": "module", "private": true, "version": "1.0.0", "description": "Book court from onepa website", "main": "../src/scrape.mjs", "scripts": {"scrape": "node --experimental-modules --es-module-specifier-resolution=node ./run/index.mjs", "build": "webpack"}, "repository": {"type": "git", "url": "git+https://github.com/AnuoluwapoAR/<PERSON>-david-bot.git"}, "author": "Anuoluwapo AR", "license": "ISC", "bugs": {"url": "https://github.com/AnuoluwapoAR/<PERSON>-david-bot/issues"}, "homepage": "https://github.com/AnuoluwapoAR/<PERSON>-david-bot#readme", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,jsx}": ["pretty-quick --staged", "eslint", "git add"]}, "dependencies": {"core-js": "^3.21.1", "dotenv": "^16.0.0", "regenerator-runtime": "^0.13.9", "spider-core": "^1.2.2"}, "devDependencies": {"@babel/core": "^7.17.5", "@babel/eslint-parser": "^7.17.0", "@babel/plugin-proposal-export-default-from": "^7.16.7", "@babel/plugin-proposal-object-rest-spread": "^7.17.3", "@babel/preset-env": "^7.16.11", "acorn": "^8.7.0", "babel-loader": "^8.2.3", "babel-plugin-import": "^1.13.3", "bufferutil": "^4.0.6", "compression-webpack-plugin": "^9.2.0", "css-loader": "^6.7.0", "eslint": "^8.22.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-prettier": "^4.0.0", "husky": "^7.0.4", "lint-staged": "^12.3.5", "mini-svg-data-uri": "^1.4.3", "prettier": "^2.5.1", "pretty-quick": "^3.1.3", "style-loader": "^3.3.1", "supports-color": "^9.2.1", "utf-8-validate": "^5.0.8", "webpack": "^5.70.0", "webpack-cli": "^4.9.2"}}